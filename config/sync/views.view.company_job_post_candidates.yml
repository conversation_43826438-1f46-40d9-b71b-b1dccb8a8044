uuid: 87db8563-106e-49f1-835e-b8274aefb6ba
langcode: en
status: true
dependencies:
  config:
    - field.storage.group_relationship.field_first_name
    - field.storage.group_relationship.field_last_name
    - field.storage.group_relationship.field_status
    - group.relationship_type.company-job_application
  module:
    - group
    - options
    - views_kanban
id: company_job_post_candidates
label: 'Company job post candidates'
module: views
description: ''
tag: ''
base_table: group_relationship_field_data
base_field: id
display:
  default:
    id: default
    display_title: Default
    display_plugin: default
    position: 0
    display_options:
      title: 'Company job post candidates'
      fields:
        field_status:
          id: field_status
          table: group_relationship__field_status
          field: field_status
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: ''
          exclude: true
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: list_default
          settings: {  }
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_first_name:
          id: field_first_name
          table: group_relationship__field_first_name
          field: field_first_name
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_last_name:
          id: field_last_name
          table: group_relationship__field_last_name
          field: field_last_name
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
      pager:
        type: none
        options:
          offset: 0
      exposed_form:
        type: basic
        options:
          submit_button: Apply
          reset_button: false
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
          expose_sort_order: true
          sort_asc_label: Asc
          sort_desc_label: Desc
      access:
        type: group_permission
        options:
          group_permission: 'view job_application relationship'
      cache:
        type: tag
        options: {  }
      empty: {  }
      sorts:
        created:
          id: created
          table: group_relationship_field_data
          field: created
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: group_relationship
          entity_field: created
          plugin_id: date
          order: DESC
          expose:
            label: ''
            field_identifier: ''
          exposed: false
          granularity: second
      arguments:
        gid:
          id: gid
          table: group_relationship_field_data
          field: gid
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: group_relationship
          entity_field: gid
          plugin_id: group_id
          default_action: default
          exception:
            value: all
            title_enable: false
            title: All
          title_enable: false
          title: ''
          default_argument_type: group_id_from_url
          default_argument_options: {  }
          summary_options:
            base_path: ''
            count: true
            override: false
            items_per_page: 25
          summary:
            sort_order: asc
            number_of_records: 0
            format: default_summary
          specify_validation: false
          validate:
            type: none
            fail: 'not found'
          validate_options: {  }
          break_phrase: false
          not: false
        field_job_post_target_id:
          id: field_job_post_target_id
          table: group_relationship__field_job_post
          field: field_job_post_target_id
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: entity_target_id
          default_action: default
          exception:
            value: all
            title_enable: false
            title: All
          title_enable: false
          title: ''
          default_argument_type: raw
          default_argument_options:
            index: 3
            use_alias: false
          summary_options:
            base_path: ''
            count: true
            override: false
            items_per_page: 25
          summary:
            sort_order: asc
            number_of_records: 0
            format: default_summary
          specify_validation: false
          validate:
            type: none
            fail: 'not found'
          validate_options: {  }
          break_phrase: false
          not: false
      filters:
        type:
          id: type
          table: group_relationship_field_data
          field: type
          entity_type: group_relationship
          entity_field: type
          plugin_id: bundle
          value:
            company-job_application: company-job_application
      style:
        type: kanban
        options:
          status_field: field_status
          progress_field: ''
          title_field: field_first_name
          assign_field: ''
          history_field: ''
          send_email: false
          total_field: ''
          date_field: ''
          dialog_width: 80%
          disable_dragdrop: false
          disable_add: true
          show_hide_columns: false
          send_notification: false
      row:
        type: fields
      query:
        type: views_query
        options:
          query_comment: ''
          disable_sql_rewrite: false
          distinct: false
          replica: false
          query_tags: {  }
      relationships:
        gid:
          id: gid
          table: group_relationship_field_data
          field: gid
          relationship: none
          group_type: group
          admin_label: Group
          entity_type: group_relationship
          entity_field: gid
          plugin_id: standard
          required: true
      header: {  }
      footer: {  }
      display_extenders: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - route
        - route.group
        - url
        - user.group_permissions
      tags:
        - 'config:field.storage.group_relationship.field_first_name'
        - 'config:field.storage.group_relationship.field_last_name'
        - 'config:field.storage.group_relationship.field_status'
  page_1:
    id: page_1
    display_title: Page
    display_plugin: page
    position: 1
    display_options:
      display_extenders: {  }
      path: 'company/{group}/job/{node}/candidates'
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - route
        - route.group
        - url
        - user.group_permissions
      tags:
        - 'config:field.storage.group_relationship.field_first_name'
        - 'config:field.storage.group_relationship.field_last_name'
        - 'config:field.storage.group_relationship.field_status'
