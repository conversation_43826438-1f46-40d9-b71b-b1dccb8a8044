uuid: 5f4dd767-5028-4702-9d69-a3f0b9acca59
langcode: bg
status: true
dependencies: {  }
_core:
  default_config_hash: puB8VCKbCpDzJ-dEd9mlRUEShkBB7TQLAPiTBxCYtQs
id: text_phonetic_und_7_0_0
label: 'Fulltext Phonetic'
minimum_solr_version: 7.0.0
custom_code: phonetic
field_type_language_code: und
domains: {  }
field_type:
  name: text_phonetic_und
  class: solr.TextField
  positionIncrementGap: 100
  storeOffsetsWithPositions: true
  analyzers:
    -
      type: index
      tokenizer:
        class: solr.StandardTokenizerFactory
      filters:
        -
          class: solr.StopFilterFactory
          ignoreCase: true
          words: stopwords_und.txt
        -
          class: solr.WordDelimiterGraphFilterFactory
          catenateNumbers: 1
          generateNumberParts: 1
          protected: protwords_und.txt
          splitOnCaseChange: 0
          generateWordParts: 1
          preserveOriginal: 1
          catenateAll: 0
          catenateWords: 1
        -
          class: solr.FlattenGraphFilterFactory
        -
          class: solr.LengthFilterFactory
          min: 2
          max: 100
        -
          class: solr.LowerCaseFilterFactory
        -
          class: solr.BeiderMorseFilterFactory
          languageSet: auto
          nameType: GENERIC
          ruleType: APPROX
          concat: true
        -
          class: solr.RemoveDuplicatesTokenFilterFactory
    -
      type: query
      tokenizer:
        class: solr.StandardTokenizerFactory
      filters:
        -
          class: solr.StopFilterFactory
          ignoreCase: true
          words: stopwords_und.txt
        -
          class: solr.WordDelimiterGraphFilterFactory
          catenateNumbers: 0
          generateNumberParts: 1
          protected: protwords_und.txt
          splitOnCaseChange: 0
          generateWordParts: 1
          preserveOriginal: 1
          catenateAll: 0
          catenateWords: 0
        -
          class: solr.LengthFilterFactory
          min: 2
          max: 100
        -
          class: solr.LowerCaseFilterFactory
        -
          class: solr.BeiderMorseFilterFactory
          languageSet: auto
          nameType: GENERIC
          ruleType: APPROX
          concat: true
        -
          class: solr.RemoveDuplicatesTokenFilterFactory
unstemmed_field_type: null
spellcheck_field_type: null
collated_field_type: null
solr_configs: {  }
text_files: {  }
