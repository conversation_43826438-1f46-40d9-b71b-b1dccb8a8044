uuid: 2872acab-f0ec-47ad-b72a-383808e9b387
langcode: bg
status: true
dependencies: {  }
_core:
  default_config_hash: hPeNZvfaGs133o23oF-cTmIUmmQm18VwkOjZz5BY9N0
id: text_bg_7_0_0
label: 'Bulgarian Text Field'
minimum_solr_version: 7.0.0
custom_code: ''
field_type_language_code: bg
domains: {  }
field_type:
  name: text_bg
  class: solr.TextField
  positionIncrementGap: 100
  storeOffsetsWithPositions: true
  analyzers:
    -
      type: index
      tokenizer:
        class: solr.StandardTokenizerFactory
      filters:
        -
          class: solr.WordDelimiterGraphFilterFactory
          catenateNumbers: 0
          generateNumberParts: 0
          protected: protwords_bg.txt
          splitOnCaseChange: 1
          generateWordParts: 1
          preserveOriginal: 1
          catenateAll: 0
          catenateWords: 0
        -
          class: solr.FlattenGraphFilterFactory
        -
          class: solr.LowerCaseFilterFactory
        -
          class: solr.StopFilterFactory
          ignoreCase: true
          words: stopwords_bg.txt
        -
          class: solr.BulgarianStemFilterFactory
        -
          class: solr.RemoveDuplicatesTokenFilterFactory
    -
      type: query
      tokenizer:
        class: solr.StandardTokenizerFactory
      filters:
        -
          class: solr.WordDelimiterGraphFilterFactory
          catenateNumbers: 0
          generateNumberParts: 0
          protected: protwords_bg.txt
          splitOnCaseChange: 1
          generateWordParts: 1
          preserveOriginal: 1
          catenateAll: 0
          catenateWords: 0
        -
          class: solr.LowerCaseFilterFactory
        -
          class: solr.StopFilterFactory
          ignoreCase: true
          words: stopwords_bg.txt
        -
          class: solr.SynonymGraphFilterFactory
          ignoreCase: true
          synonyms: synonyms_bg.txt
          expand: true
        -
          class: solr.BulgarianStemFilterFactory
        -
          class: solr.RemoveDuplicatesTokenFilterFactory
unstemmed_field_type:
  name: text_unstemmed_bg
  class: solr.TextField
  positionIncrementGap: 100
  storeOffsetsWithPositions: true
  analyzers:
    -
      type: index
      tokenizer:
        class: solr.StandardTokenizerFactory
      filters:
        -
          class: solr.WordDelimiterGraphFilterFactory
          catenateNumbers: 0
          generateNumberParts: 0
          protected: protwords_bg.txt
          splitOnCaseChange: 1
          generateWordParts: 1
          preserveOriginal: 1
          catenateAll: 0
          catenateWords: 0
        -
          class: solr.FlattenGraphFilterFactory
        -
          class: solr.LowerCaseFilterFactory
        -
          class: solr.StopFilterFactory
          ignoreCase: true
          words: stopwords_bg.txt
        -
          class: solr.RemoveDuplicatesTokenFilterFactory
    -
      type: query
      tokenizer:
        class: solr.StandardTokenizerFactory
      filters:
        -
          class: solr.WordDelimiterGraphFilterFactory
          catenateNumbers: 0
          generateNumberParts: 0
          protected: protwords_bg.txt
          splitOnCaseChange: 1
          generateWordParts: 1
          preserveOriginal: 1
          catenateAll: 0
          catenateWords: 0
        -
          class: solr.LowerCaseFilterFactory
        -
          class: solr.StopFilterFactory
          ignoreCase: true
          words: stopwords_bg.txt
        -
          class: solr.SynonymGraphFilterFactory
          ignoreCase: true
          synonyms: synonyms_bg.txt
          expand: true
        -
          class: solr.RemoveDuplicatesTokenFilterFactory
spellcheck_field_type: null
collated_field_type:
  name: collated_bg
  class: solr.ICUCollationField
  locale: bg
  strength: primary
  caseLevel: false
solr_configs:
  searchComponents:
    -
      name: spellcheck
      class: solr.SpellCheckComponent
      lst:
        -
          name: spellchecker
          str:
            -
              name: name
              VALUE: bg
            -
              name: field
              VALUE: spellcheck_bg
            -
              name: classname
              VALUE: solr.DirectSolrSpellChecker
            -
              name: distanceMeasure
              VALUE: internal
            -
              name: accuracy
              VALUE: '0.5'
            -
              name: maxEdits
              VALUE: '2'
            -
              name: minPrefix
              VALUE: '1'
            -
              name: maxInspections
              VALUE: '5'
            -
              name: minQueryLength
              VALUE: '4'
            -
              name: maxQueryFrequency
              VALUE: '0.01'
            -
              name: thresholdTokenFrequency
              VALUE: '.01'
            -
              name: onlyMorePopular
              VALUE: 'true'
    -
      name: suggest
      class: solr.SuggestComponent
      lst:
        -
          name: suggester
          str:
            -
              name: name
              VALUE: bg
            -
              name: indexPath
              VALUE: ./bg
            -
              name: lookupImpl
              VALUE: AnalyzingInfixLookupFactory
            -
              name: dictionaryImpl
              VALUE: DocumentDictionaryFactory
            -
              name: field
              VALUE: twm_suggest
            -
              name: suggestAnalyzerFieldType
              VALUE: text_bg
            -
              name: contextField
              VALUE: sm_context_tags
            -
              name: buildOnCommit
              VALUE: 'false'
            -
              name: buildOnStartup
              VALUE: 'false'
text_files:
  stopwords: |
    а
    аз
    ако
    ала
    бе
    без
    беше
    би
    бил
    била
    били
    било
    близо
    бъдат
    бъде
    бяха
    в
    вас
    ваш
    ваша
    вероятно
    вече
    взема
    ви
    вие
    винаги
    все
    всеки
    всички
    всичко
    всяка
    във
    въпреки
    върху
    г
    ги
    главно
    го
    д
    да
    дали
    до
    докато
    докога
    дори
    досега
    доста
    е
    едва
    един
    ето
    за
    зад
    заедно
    заради
    засега
    затова
    защо
    защото
    и
    из
    или
    им
    има
    имат
    иска
    й
    каза
    как
    каква
    какво
    както
    какъв
    като
    кога
    когато
    което
    които
    кой
    който
    колко
    която
    къде
    където
    към
    ли
    м
    ме
    между
    мен
    ми
    мнозина
    мога
    могат
    може
    моля
    момента
    му
    н
    на
    над
    назад
    най
    направи
    напред
    например
    нас
    не
    него
    нея
    ни
    ние
    никой
    нито
    но
    някои
    някой
    няма
    обаче
    около
    освен
    особено
    от
    отгоре
    отново
    още
    пак
    по
    повече
    повечето
    под
    поне
    поради
    после
    почти
    прави
    пред
    преди
    през
    при
    пък
    първо
    с
    са
    само
    се
    сега
    си
    скоро
    след
    сме
    според
    сред
    срещу
    сте
    съм
    със
    също
    т
    тази
    така
    такива
    такъв
    там
    твой
    те
    тези
    ти
    тн
    то
    това
    тогава
    този
    той
    толкова
    точно
    трябва
    тук
    тъй
    тя
    тях
    у
    харесва
    ч
    че
    често
    чрез
    ще
    щом
    я
  protwords: |

  accents: ''
  synonyms: |
    drupal, durpal
  nouns: ''
