uuid: 7e2a1b2f-b99e-4ceb-af9b-a8a2dda90e8b
langcode: bg
status: true
dependencies: {  }
_core:
  default_config_hash: n-FuFxlplOQec-lc4SrvoR5ne_l_ORg0H75pc1-yY2Q
id: text_phonetic_en_7_0_0
label: 'Fulltext Phonetic English'
minimum_solr_version: 7.0.0
custom_code: phonetic
field_type_language_code: en
domains: {  }
field_type:
  name: text_phonetic_en
  class: solr.TextField
  positionIncrementGap: 100
  storeOffsetsWithPositions: true
  analyzers:
    -
      type: index
      tokenizer:
        class: solr.StandardTokenizerFactory
      filters:
        -
          class: solr.StopFilterFactory
          ignoreCase: true
          words: stopwords_en.txt
        -
          class: solr.WordDelimiterGraphFilterFactory
          catenateNumbers: 1
          generateNumberParts: 1
          protected: protwords_en.txt
          splitOnCaseChange: 0
          generateWordParts: 1
          preserveOriginal: 1
          catenateAll: 0
          catenateWords: 1
        -
          class: solr.FlattenGraphFilterFactory
        -
          class: solr.LengthFilterFactory
          min: 2
          max: 100
        -
          class: solr.LowerCaseFilterFactory
        -
          class: solr.BeiderMorseFilterFactory
          languageSet: english
          nameType: GENERIC
          ruleType: APPROX
          concat: true
        -
          class: solr.RemoveDuplicatesTokenFilterFactory
    -
      type: query
      tokenizer:
        class: solr.StandardTokenizerFactory
      filters:
        -
          class: solr.StopFilterFactory
          ignoreCase: true
          words: stopwords_en.txt
        -
          class: solr.WordDelimiterGraphFilterFactory
          catenateNumbers: 0
          generateNumberParts: 1
          protected: protwords_en.txt
          splitOnCaseChange: 0
          generateWordParts: 1
          preserveOriginal: 1
          catenateAll: 0
          catenateWords: 0
        -
          class: solr.LengthFilterFactory
          min: 2
          max: 100
        -
          class: solr.LowerCaseFilterFactory
        -
          class: solr.BeiderMorseFilterFactory
          languageSet: english
          nameType: GENERIC
          ruleType: APPROX
          concat: true
        -
          class: solr.RemoveDuplicatesTokenFilterFactory
unstemmed_field_type: null
spellcheck_field_type: null
collated_field_type: null
solr_configs: {  }
text_files: {  }
