uuid: a773fcd3-aeff-4783-b7c0-54dc619cb33b
langcode: bg
status: true
dependencies:
  config:
    - field.storage.message.field_message_pm_thread
    - message.template.private_message_notification
  enforced:
    module:
      - private_message
_core:
  default_config_hash: BO2Gdo863iS_b5LBhpA4WCkS7ax6Tqpd_YcoIAZv_qs
id: message.private_message_notification.field_message_pm_thread
field_name: field_message_pm_thread
entity_type: message
bundle: private_message_notification
label: 'Private Message Thread'
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:private_message_thread'
  handler_settings:
    target_bundles: null
    sort:
      field: _none
    auto_create: false
field_type: entity_reference
