uuid: 72ae472b-e702-46f4-a835-4bfad6eb7a9b
langcode: bg
status: true
dependencies: {  }
_core:
  default_config_hash: bWSErrOZirxgC9CR3TdPlJXHNmex1pARA9RNC--VqY8
id: text_edgestring_und_6_0_0
label: 'Edge NGram String Field'
minimum_solr_version: 6.0.0
custom_code: edgestring
field_type_language_code: und
domains: {  }
field_type:
  name: text_edgenstring
  class: solr.TextField
  positionIncrementGap: 100
  storeOffsetsWithPositions: true
  analyzers:
    -
      type: index
      tokenizer:
        class: solr.KeywordTokenizerFactory
      filters:
        -
          class: solr.LowerCaseFilterFactory
        -
          class: solr.RemoveDuplicatesTokenFilterFactory
        -
          class: solr.EdgeNGramFilterFactory
          minGramSize: 2
          maxGramSize: 25
    -
      type: query
      tokenizer:
        class: solr.KeywordTokenizerFactory
      filters:
        -
          class: solr.LowerCaseFilterFactory
        -
          class: solr.RemoveDuplicatesTokenFilterFactory
unstemmed_field_type: null
spellcheck_field_type: null
collated_field_type: null
solr_configs: {  }
text_files: {  }
