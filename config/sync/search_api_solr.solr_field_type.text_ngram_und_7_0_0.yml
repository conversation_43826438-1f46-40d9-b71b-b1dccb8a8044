uuid: 14f24368-f441-41b0-bc6e-a7b0e4ac1ba2
langcode: bg
status: true
dependencies: {  }
_core:
  default_config_hash: wr_ZSGx3pe-AgupW-Sz4q3a5tyhG5E0yg6IySnGjwW8
id: text_ngram_und_7_0_0
label: 'NGram Text Field'
minimum_solr_version: 7.0.0
custom_code: ngram
field_type_language_code: und
domains: {  }
field_type:
  name: text_ngram
  class: solr.TextField
  positionIncrementGap: 100
  storeOffsetsWithPositions: true
  analyzers:
    -
      type: index
      charFilters:
        -
          class: solr.MappingCharFilterFactory
          mapping: accents_und.txt
      tokenizer:
        class: solr.StandardTokenizerFactory
      filters:
        -
          class: solr.StopFilterFactory
          ignoreCase: true
          words: stopwords_und.txt
        -
          class: solr.WordDelimiterGraphFilterFactory
          catenateNumbers: 1
          generateNumberParts: 1
          protected: protwords_und.txt
          splitOnCaseChange: 0
          generateWordParts: 1
          preserveOriginal: 1
          catenateAll: 0
          catenateWords: 1
        -
          class: solr.FlattenGraphFilterFactory
        -
          class: solr.LengthFilterFactory
          min: 2
          max: 100
        -
          class: solr.LowerCaseFilterFactory
        -
          class: solr.RemoveDuplicatesTokenFilterFactory
        -
          class: solr.NGramFilterFactory
          minGramSize: 2
          maxGramSize: 25
    -
      type: query
      charFilters:
        -
          class: solr.MappingCharFilterFactory
          mapping: accents_und.txt
      tokenizer:
        class: solr.StandardTokenizerFactory
      filters:
        -
          class: solr.LengthFilterFactory
          min: 2
          max: 100
        -
          class: solr.LowerCaseFilterFactory
        -
          class: solr.RemoveDuplicatesTokenFilterFactory
unstemmed_field_type: null
spellcheck_field_type: null
collated_field_type: null
solr_configs: {  }
text_files: {  }
