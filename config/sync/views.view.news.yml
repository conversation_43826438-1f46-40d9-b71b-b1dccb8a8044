uuid: 15c6d0ec-0a1c-46be-8622-67873d9588ae
langcode: en
status: true
dependencies:
  config:
    - core.entity_view_mode.node.featured
    - core.entity_view_mode.node.teaser
    - node.type.news
  module:
    - node
    - ui_patterns
    - ui_patterns_views
    - user
  theme:
    - bwy
id: news
label: News
module: views
description: ''
tag: ''
base_table: node_field_data
base_field: nid
display:
  default:
    id: default
    display_title: Default
    display_plugin: default
    position: 0
    display_options:
      title: News
      fields:
        title:
          id: title
          table: node_field_data
          field: title
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: title
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            make_link: false
            absolute: false
            word_boundary: false
            ellipsis: false
            strip_tags: false
            trim: false
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: true
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
      pager:
        type: mini
        options:
          offset: 0
          pagination_heading_level: h4
          items_per_page: 10
          total_pages: null
          id: 0
          tags:
            next: ››
            previous: ‹‹
          expose:
            items_per_page: false
            items_per_page_label: 'Items per page'
            items_per_page_options: '5, 10, 25, 50'
            items_per_page_options_all: false
            items_per_page_options_all_label: '- All -'
            offset: false
            offset_label: Offset
      exposed_form:
        type: basic
        options:
          submit_button: Apply
          reset_button: false
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
          expose_sort_order: true
          sort_asc_label: Asc
          sort_desc_label: Desc
      access:
        type: perm
        options:
          perm: 'access content'
      cache:
        type: tag
        options: {  }
      empty: {  }
      sorts:
        created:
          id: created
          table: node_field_data
          field: created
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: created
          plugin_id: date
          order: DESC
          expose:
            label: ''
            field_identifier: ''
          exposed: false
          granularity: second
      arguments: {  }
      filters:
        status:
          id: status
          table: node_field_data
          field: status
          entity_type: node
          entity_field: status
          plugin_id: boolean
          value: '1'
          group: 1
          expose:
            operator: ''
        type:
          id: type
          table: node_field_data
          field: type
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: type
          plugin_id: bundle
          operator: in
          value:
            news: news
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
      style:
        type: default
      row:
        type: 'entity:node'
        options:
          relationship: none
          view_mode: featured
      query:
        type: views_query
        options:
          query_comment: ''
          disable_sql_rewrite: false
          distinct: false
          replica: false
          query_tags: {  }
      relationships: {  }
      css_class: ''
      header: {  }
      footer: {  }
      display_extenders: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url.query_args
        - 'user.node_grants:view'
        - user.permissions
      tags: {  }
  all_news:
    id: all_news
    display_title: 'All news'
    display_plugin: attachment
    position: 2
    display_options:
      pager:
        type: full
        options:
          offset: 1
          pagination_heading_level: h4
          items_per_page: 9
          total_pages: null
          id: 0
          tags:
            next: 'Next ›'
            previous: '‹ Previous'
            first: '« First'
            last: 'Last »'
          expose:
            items_per_page: false
            items_per_page_label: 'Items per page'
            items_per_page_options: '5, 10, 25, 50'
            items_per_page_options_all: false
            items_per_page_options_all_label: '- All -'
            offset: false
            offset_label: Offset
          quantity: 7
      style:
        type: ui_patterns
        options:
          uses_fields: false
          ui_patterns:
            ui_patterns:
              component_id: 'bwy:grid_cols'
              variant_id: null
              props:
                attributes:
                  source_id: attributes
                  source:
                    value: 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-x-8 gap-y-10'
                grid_classes:
                  source_id: textfield
                  source:
                    value: ''
              slots:
                content:
                  sources:
                    -
                      source_id: view_rows
                      source:
                        ui_patterns_views_field: ''
                      _weight: '0'
      row:
        type: 'entity:node'
        options:
          relationship: none
          view_mode: teaser
      defaults:
        style: false
        row: false
        header: false
      display_description: ''
      header:
        area_text_custom:
          id: area_text_custom
          table: views
          field: area_text_custom
          relationship: none
          group_type: group
          admin_label: Heading
          plugin_id: text_custom
          empty: false
          content: "<div class=\"flex items-center mb-10\">\r\n  <img src=\"[site:base-path]/themes/custom/bwy/dist/assets/icons/logo-symbol-pink.svg\" alt=\"\" />\r\n  <h2 class=\"heading-1 text-text-main ml-5\">All News</h2>\r\n</div>"
          tokenize: false
      display_extenders: {  }
      displays:
        feature_news: feature_news
      attachment_position: after
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url.query_args
        - 'user.node_grants:view'
        - user.permissions
      tags: {  }
  feature_news:
    id: feature_news
    display_title: 'Featured news'
    display_plugin: page
    position: 1
    display_options:
      pager:
        type: some
        options:
          offset: 0
          items_per_page: 1
      defaults:
        pager: false
        header: false
      display_description: ''
      header: {  }
      display_extenders: {  }
      path: news
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - 'user.node_grants:view'
        - user.permissions
      tags: {  }
  news_home:
    id: news_home
    display_title: 'News Home page'
    display_plugin: block
    position: 2
    display_options:
      pager:
        type: some
        options:
          offset: 0
          items_per_page: 4
      style:
        type: ui_patterns
        options:
          uses_fields: false
          ui_patterns:
            ui_patterns:
              component_id: 'bwy:grid_cols'
              variant_id: null
              props:
                attributes:
                  source_id: attributes
                  source:
                    value: 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-x-8 gap-y-10'
                grid_classes:
                  source_id: textfield
                  source:
                    value: ''
              slots:
                content:
                  sources:
                    -
                      source_id: view_rows
                      source:
                        ui_patterns_views_field: ''
                      _weight: '0'
      row:
        type: 'entity:node'
        options:
          relationship: none
          view_mode: teaser
      defaults:
        pager: false
        style: false
        row: false
        header: false
      display_description: ''
      header:
        display_link:
          id: display_link
          table: views
          field: display_link
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: display_link
          label: 'See all'
          empty: false
          display_id: feature_news
      display_extenders: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - 'user.node_grants:view'
        - user.permissions
      tags: {  }
  related_news:
    id: related_news
    display_title: 'Related news'
    display_plugin: block
    position: 3
    display_options:
      title: 'More news'
      pager:
        type: some
        options:
          offset: 0
          items_per_page: 3
      arguments:
        nid:
          id: nid
          table: node_field_data
          field: nid
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: nid
          plugin_id: node_nid
          default_action: default
          exception:
            value: all
            title_enable: false
            title: All
          title_enable: false
          title: ''
          default_argument_type: node
          default_argument_options: {  }
          summary_options:
            base_path: ''
            count: true
            override: false
            items_per_page: 25
          summary:
            sort_order: asc
            number_of_records: 0
            format: default_summary
          specify_validation: false
          validate:
            type: none
            fail: 'not found'
          validate_options: {  }
          break_phrase: false
          not: true
      style:
        type: ui_patterns
        options:
          uses_fields: false
          ui_patterns:
            ui_patterns:
              component_id: 'bwy:grid_cols'
              variant_id: null
              props:
                attributes:
                  source_id: attributes
                  source:
                    value: 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-x-8 gap-y-10'
              slots:
                content:
                  sources:
                    -
                      source_id: view_rows
                      source:
                        ui_patterns_views_field: ''
                      _weight: '0'
      row:
        type: 'entity:node'
        options:
          relationship: none
          view_mode: teaser
      defaults:
        title: false
        pager: false
        style: false
        row: false
        arguments: false
        header: false
      display_description: ''
      header:
        display_link:
          id: display_link
          table: views
          field: display_link
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: display_link
          label: 'See all'
          empty: false
          display_id: feature_news
      display_extenders: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - 'user.node_grants:view'
        - user.permissions
      tags: {  }
