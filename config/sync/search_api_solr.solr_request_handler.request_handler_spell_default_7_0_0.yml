uuid: bae7b346-18fe-4f32-a5f4-a80fd20ea6cb
langcode: bg
status: true
dependencies: {  }
_core:
  default_config_hash: K8FL_GAJEdbxEnnmGUmM69c7O38bwz66axaebjmsb4U
id: request_handler_spell_default_7_0_0
label: Spellcheck
minimum_solr_version: 7.0.0
environments: {  }
recommended: true
request_handler:
  name: /spell
  class: solr.SearchHandler
  startup: lazy
  lst:
    -
      name: defaults
      str:
        -
          name: df
          VALUE: id
        -
          name: spellcheck.dictionary
          VALUE: und
        -
          name: spellcheck
          VALUE: 'on'
        -
          name: spellcheck.onlyMorePopular
          VALUE: 'false'
        -
          name: spellcheck.extendedResults
          VALUE: 'false'
        -
          name: spellcheck.count
          VALUE: '1'
        -
          name: spellcheck.alternativeTermCount
          VALUE: '5'
        -
          name: spellcheck.maxResultsForSuggest
          VALUE: '5'
        -
          name: spellcheck.collate
          VALUE: 'true'
        -
          name: spellcheck.collateExtendedResults
          VALUE: 'true'
        -
          name: spellcheck.maxCollationTries
          VALUE: '10'
        -
          name: spellcheck.maxCollations
          VALUE: '5'
  arr:
    -
      name: last-components
      str:
        -
          VALUE: spellcheck
solr_configs: null
