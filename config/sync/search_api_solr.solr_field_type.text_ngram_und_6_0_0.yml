uuid: ccfffa7c-11ef-406b-a463-8ef0c6215e02
langcode: bg
status: true
dependencies: {  }
_core:
  default_config_hash: nQ6v8pgrKN3bzfcel5GP6EO6A1Yo5uRUQId9pOyds6U
id: text_ngram_und_6_0_0
label: 'NGram Text Field'
minimum_solr_version: 6.0.0
custom_code: ngram
field_type_language_code: und
domains: {  }
field_type:
  name: text_ngram
  class: solr.TextField
  positionIncrementGap: 100
  storeOffsetsWithPositions: true
  analyzers:
    -
      type: index
      charFilters:
        -
          class: solr.MappingCharFilterFactory
          mapping: accents_und.txt
      tokenizer:
        class: solr.StandardTokenizerFactory
      filters:
        -
          class: solr.StopFilterFactory
          ignoreCase: true
          words: stopwords_und.txt
        -
          class: solr.WordDelimiterFilterFactory
          catenateNumbers: 1
          generateNumberParts: 1
          protected: protwords_und.txt
          splitOnCaseChange: 0
          generateWordParts: 1
          preserveOriginal: 1
          catenateAll: 0
          catenateWords: 1
        -
          class: solr.LengthFilterFactory
          min: 2
          max: 100
        -
          class: solr.LowerCaseFilterFactory
        -
          class: solr.RemoveDuplicatesTokenFilterFactory
        -
          class: solr.NGramFilterFactory
          minGramSize: 2
          maxGramSize: 25
    -
      type: query
      charFilters:
        -
          class: solr.MappingCharFilterFactory
          mapping: accents_und.txt
      tokenizer:
        class: solr.StandardTokenizerFactory
      filters:
        -
          class: solr.LengthFilterFactory
          min: 2
          max: 100
        -
          class: solr.LowerCaseFilterFactory
        -
          class: solr.RemoveDuplicatesTokenFilterFactory
unstemmed_field_type: null
spellcheck_field_type: null
collated_field_type: null
solr_configs: {  }
text_files: {  }
